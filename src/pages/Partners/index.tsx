import { useState, useEffect, useMemo } from 'react';
import { ToastContainer } from 'react-toastify';
import cn from 'classnames';

import { AuthService, ClientService, UserService, PartnersService, PartnerResponse, ApiKey, RawApiKey } from 'services';
import { UserInterface, ClientInterface } from 'interfaces';
import Button from 'shared/Button';
import AddPartnerModal from 'components/AddPartnerModal';
import EditPartnerModal from 'components/EditPartnerModal';
import ApiKeyModal from 'components/ApiKeyModal';
import ManageApiKeysModal from 'components/ManageApiKeysModal';

import pageClasses from 'styles/PageWrapper.module.scss';
import tableClasses from 'shared/Table/Table.module.scss';
import classes from './Partners.module.scss';
import PageLayout from 'shared/PageLayout';

interface PartnerInterface {
  id: string;
  name: string;
  features: string[];
  clientIds: string[];
  enabled: boolean;
  apiEnabled: boolean;
  apiKeyIds: string[];
  apiKeys: ApiKey[];
  rawApiKeys: RawApiKey[];
  createdAt: string;
  updatedAt: string;
}

function Partners(props: {
  clientService?: ClientService,
  authService: AuthService,
  userService?: UserService,
  authUser?: UserInterface
}) {
  const { clientService, authService, authUser } = props;

  // Create partners service instance - memoized to prevent recreation on every render
  const partnersService = useMemo(() => {
    const currentToken = localStorage.getItem('token') || authService.token;
    const clientId = JSON.parse(localStorage.getItem('client') || '{}')?.id;
    return new PartnersService(currentToken, clientId);
  }, [authService.token]);

  const [client] = useState<ClientInterface | null>(null);
  const [clients, setClients] = useState<ClientInterface[]>([]);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingPartner, setEditingPartner] = useState<PartnerInterface | null>(null);
  const [isApiKeyModalOpen, setIsApiKeyModalOpen] = useState(false);
  const [newPartnerData, setNewPartnerData] = useState<{ name: string; rawApiKeys: RawApiKey[]; isNewClientKeys?: boolean } | null>(null);
  const [isManageApiKeysModalOpen, setIsManageApiKeysModalOpen] = useState(false);
  const [selectedPartnerForManagement, setSelectedPartnerForManagement] = useState<PartnerInterface | null>(null);

  const [partners, setPartners] = useState<PartnerInterface[]>([]);
  const [isLoadingPartners, setIsLoadingPartners] = useState(true);

  // Fetch clients and partners on component mount
  useEffect(() => {
    let isMounted = true; // Flag to prevent state updates if component unmounts

    const fetchData = async () => {
      // Fetch clients
      if (clientService && isMounted) {
        try {
          await clientService.getAllClients((fetchedClients: ClientInterface[]) => {
            if (isMounted) {
              setClients(fetchedClients);
            }
          });
        } catch (error) {
          console.error('Error fetching clients:', error);
        }
      }

      // Fetch partners
      if (isMounted) {
        try {
          await partnersService.getAllPartners((fetchedPartners: PartnerInterface[]) => {
            if (isMounted) {
              setPartners(fetchedPartners);
              setIsLoadingPartners(false);
            }
          });
        } catch (error) {
          console.error('Error fetching partners:', error);
          if (isMounted) {
            setIsLoadingPartners(false);
          }
        }
      }
    };

    fetchData();

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, []); // Empty dependency array - only run once on mount

  const handleEditPartner = (partner: PartnerInterface) => {
    setEditingPartner(partner);
    setIsEditModalOpen(true);
  };

  const handleManageApiKeys = (partner: PartnerInterface) => {
    setSelectedPartnerForManagement(partner);
    setIsManageApiKeysModalOpen(true);
  };

  const handleApiKeysUpdated = (updatedApiKeys: ApiKey[]) => {
    if (selectedPartnerForManagement) {
      // Update the partner in the local state with new API keys
      setPartners(partners.map(p =>
        p.id === selectedPartnerForManagement.id
          ? { ...p, apiKeys: updatedApiKeys }
          : p
      ));
      // Update the selected partner for management
      setSelectedPartnerForManagement({ ...selectedPartnerForManagement, apiKeys: updatedApiKeys });
    }
  };

  const handleShowNewApiKey = (newApiKey: { clientId: string; rawKey: string; keySample: string }) => {
    // Close the manage modal and show the new API key
    setIsManageApiKeysModalOpen(false);
    setNewPartnerData({
      name: selectedPartnerForManagement?.name || '',
      rawApiKeys: [{ clientId: newApiKey.clientId, rawKey: newApiKey.rawKey, apiKeyId: '' }],
      isNewClientKeys: false
    });
    setIsApiKeyModalOpen(true);
  };

  const handleEditSave = (updatedPartner: PartnerResponse, newClientApiKeys?: RawApiKey[]) => {
    // Update the partner in the local state
    setPartners(partners.map(p => p.id === updatedPartner.id ? updatedPartner as PartnerInterface : p));
    setIsEditModalOpen(false);
    setEditingPartner(null);

    // Show API key modal if new clients were added
    if (newClientApiKeys && newClientApiKeys.length > 0) {
      setNewPartnerData({ name: updatedPartner.name, rawApiKeys: newClientApiKeys, isNewClientKeys: true });
      setIsApiKeyModalOpen(true);
    }
  };

  const handleAddPartner = (createdPartner: PartnerInterface) => {
    // Add the new partner to the local state
    setPartners([...partners, createdPartner]);
    setIsAddModalOpen(false);

    // Show API key modal with all raw API keys
    setNewPartnerData({ name: createdPartner.name, rawApiKeys: createdPartner.rawApiKeys, isNewClientKeys: false });
    setIsApiKeyModalOpen(true);
  };

  return <PageLayout user={authUser} client={client} authService={authService} className={classes.Partners}>
    <div>
      <ToastContainer position="top-right" />

      <div className={cn(pageClasses.Page, pageClasses.wide)}>
        <div className={cn(pageClasses.Wrapper)}>
          <h1 className={pageClasses.title}>
            Partners

            <Button text="Add Partner" iconText='plus' callback={() => {
              setIsAddModalOpen(true);
            }} />
          </h1>
          <div className={cn(tableClasses.TableScrollWrapper, partners.length <= 6 && tableClasses.selected)}>
            <table className={tableClasses.table}>
              <thead>
                <tr>
                  <th style={{ width: '60px' }}>ID</th>
                  <th style={{ width: '150px' }}>Name</th>
                  <th>Features</th>
                  <th style={{ width: '100px' }}>Clients</th>
                  <th style={{ width: '100px' }}>Enabled?</th>
                  <th style={{ width: '120px' }}>API Keys</th>
                  <th style={{ width: '60px' }}></th>
                </tr>
              </thead>
              <tbody>
                {isLoadingPartners ? (
                  <tr>
                    <td colSpan={7} className={tableClasses.center}>Loading partners...</td>
                  </tr>
                ) : partners.length === 0 ? (
                  <tr>
                    <td colSpan={7} className={tableClasses.center}>No partners found</td>
                  </tr>
                ) : (
                  partners.map((partner) => (
                    <tr key={partner.id}>
                      <td className={tableClasses.center}>
                        <b>{partner.id}</b>
                      </td>
                      <td className={tableClasses.center}>
                        <b>{partner.name}</b>
                      </td>
                      <td className={tableClasses.center}>
                        {partner.features.join(', ')}
                      </td>
                      <td className={tableClasses.center}>
                        {Array.isArray(partner.clientIds) ? partner.clientIds.join(', ') : partner.clientIds}
                      </td>
                      <td className={tableClasses.center}>
                        <b>{partner.apiEnabled ? 'Yes' : 'No'}</b>
                      </td>
                      <td className={tableClasses.center}>
                        {partner.apiKeys.length > 0 ? (
                          <div
                            className={classes.clickableApiKeys}
                            onClick={() => handleManageApiKeys(partner)}
                            title="Click to manage API keys"
                          >
                            {partner.apiKeys.map((apiKey, index) => (
                              <div key={apiKey.id}>
                                <code className={classes.keyText}>
                                  <b>****{apiKey.keySample}</b>
                                </code>
                                {index < partner.apiKeys.length - 1 && <br />}
                              </div>
                            ))}
                          </div>
                        ) : (
                          <span
                            className={classes.clickableApiKeys}
                            onClick={() => handleManageApiKeys(partner)}
                            title="Click to generate API keys for clients"
                          >
                            No API keys
                          </span>
                        )}
                      </td>
                      <td className={cn(tableClasses.center, tableClasses.buttonContainer)}>
                        <div className={tableClasses.buttonContainer}>
                          <Button
                            callback={() => handleEditPartner(partner)}
                            text=""
                            iconText="pen"
                          />
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <AddPartnerModal
        isOpen={isAddModalOpen}
        handleClose={() => setIsAddModalOpen(false)}
        onSave={handleAddPartner}
        clients={clients}
        partnersService={partnersService}
      />

      <EditPartnerModal
        isOpen={isEditModalOpen}
        handleClose={() => {
          setIsEditModalOpen(false);
          setEditingPartner(null);
        }}
        partner={editingPartner}
        onSave={handleEditSave}
        clients={clients}
        partnersService={partnersService}
      />

      <ApiKeyModal
        isOpen={isApiKeyModalOpen}
        handleClose={() => {
          setIsApiKeyModalOpen(false);
          setNewPartnerData(null);
        }}
        partnerName={newPartnerData?.name || ''}
        rawApiKeys={newPartnerData?.rawApiKeys || []}
        isNewClientKeys={newPartnerData?.isNewClientKeys || false}
      />

      <ManageApiKeysModal
        isOpen={isManageApiKeysModalOpen}
        handleClose={() => {
          setIsManageApiKeysModalOpen(false);
          setSelectedPartnerForManagement(null);
        }}
        partnerId={selectedPartnerForManagement?.id || ''}
        partnerName={selectedPartnerForManagement?.name || ''}
        clientIds={selectedPartnerForManagement?.clientIds || []}
        apiKeys={selectedPartnerForManagement?.apiKeys || []}
        clients={clients}
        partnersService={partnersService}
        onApiKeysUpdated={handleApiKeysUpdated}
        onShowNewApiKey={handleShowNewApiKey}
      />
    </div>
  </PageLayout>;
}

export default Partners;
